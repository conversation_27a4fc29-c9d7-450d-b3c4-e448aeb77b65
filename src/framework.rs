use crate::communication::MessageBus;
use crate::config::ConfigManager;
use crate::data::{DataProcessor, DataReader, DataStreamController, DataStreamService};
use crate::http::HttpServer;
use crate::matching::MatchingEngine;
use crate::types::{MarketData, Order, Trade};
use crate::websocket::{WebSocketDistributor, WebSocketServer};
use crate::{BacktestError, Result};
use std::sync::Arc;
use tokio::sync::{broadcast, mpsc};
use tokio::task::JoinHandle;
use tracing::{error, info, warn};

/// 回测框架主结构
/// 负责协调和启动所有组件
pub struct BacktestFramework {
    /// 消息总线
    message_bus: Arc<MessageBus>,
    /// 数据读取器
    data_reader: Option<DataReader>,
    /// 数据处理器
    data_processor: Option<DataProcessor>,
    /// 撮合引擎
    matching_engine: Option<MatchingEngine>,
    /// HTTP服务器
    http_server: Option<HttpServer>,
    /// WebSocket服务器
    websocket_server: Option<WebSocketServer>,
    /// WebSocket数据分发器
    websocket_distributor: Option<WebSocketDistributor>,
    /// 数据流控制器
    data_stream_controller: Option<Arc<DataStreamController>>,
    /// 数据流服务
    data_stream_service: Option<DataStreamService>,
    /// 任务句柄
    task_handles: Vec<JoinHandle<Result<()>>>,
}

impl BacktestFramework {
    /// 创建新的回测框架
    pub async fn new() -> Result<Self> {
        info!("Initializing backtest framework");

        // 创建消息总线
        let message_bus = Arc::new(MessageBus::new());

        // 启动消息总线
        message_bus.start().await?;

        Ok(Self {
            message_bus,
            data_reader: None,
            data_processor: None,
            matching_engine: None,
            http_server: None,
            websocket_server: None,
            websocket_distributor: None,
            data_stream_controller: None,
            data_stream_service: None,
            task_handles: Vec::new(),
        })
    }

    /// 初始化所有组件
    pub async fn initialize_components(&mut self) -> Result<()> {
        info!("Initializing framework components");

        self.http_server = Some(HttpServer::new()?);
        info!("✓ HTTP server initialized");

        self.websocket_server = Some(WebSocketServer::new()?);
        info!("✓ WebSocket server initialized");

        info!("All components initialized successfully");
        Ok(())
    }

    /// 初始化数据处理管道
    pub async fn initialize_data_pipeline(&mut self) -> Result<()> {
        info!("Initializing data processing pipeline");

        // 创建数据处理管道的通道
        let (data_reader_tx, data_processor_rx) = mpsc::channel::<MarketData>(1000);
        let (market_data_tx, market_data_rx) = broadcast::channel::<MarketData>(1000);
        let (order_tx, order_rx) = mpsc::channel::<Order>(1000);
        let (trade_tx, _trade_rx) = broadcast::channel::<Trade>(1000);
        let (order_update_tx, _order_update_rx) = broadcast::channel::<Order>(1000);
        let (market_data_forward_tx, market_data_forward_rx) =
            broadcast::channel::<MarketData>(1000);

        // 初始化数据读取器
        self.data_reader = Some(DataReader::new()?);
        info!("✓ Data reader initialized");

        // 初始化数据处理器
        self.data_processor = Some(DataProcessor::new(data_processor_rx, market_data_tx));
        info!("✓ Data processor initialized");

        // 初始化撮合引擎
        self.matching_engine = Some(MatchingEngine::new(
            market_data_rx,
            order_rx,
            trade_tx,
            order_update_tx,
            market_data_forward_tx,
        ));
        info!("✓ Matching engine initialized");

        // 初始化WebSocket数据分发器
        if let Some(websocket_server) = &self.websocket_server {
            let subscription_manager = websocket_server.subscription_manager();
            self.websocket_distributor = Some(WebSocketDistributor::new(
                market_data_forward_rx,
                subscription_manager,
            ));
            info!("✓ WebSocket distributor initialized");
        }

        // 初始化数据流控制器（Controller 直接依赖 Reader）
        let data_stream_controller = DataStreamController::new()?;
        data_stream_controller
            .set_output_channel(data_reader_tx)
            .await;
        let controller_arc = Arc::new(data_stream_controller);
        self.data_stream_controller = Some(controller_arc.clone());

        // 初始化数据流服务
        let data_stream_service = DataStreamService::new(controller_arc.clone());
        self.data_stream_service = Some(data_stream_service);

        // 设置到全局状态
        crate::state::set_data_stream_controller(controller_arc).await;
        info!("✓ Data stream controller and service initialized");

        // 存储未使用的发送端（在实际应用中可能需要传递给其他组件）
        drop(order_tx);

        info!("Data processing pipeline initialized successfully");
        Ok(())
    }

    /// 启动所有组件
    pub async fn start(&mut self) -> Result<()> {
        info!("Starting backtest framework");

        // 确保基础组件已初始化
        if self.http_server.is_none() || self.websocket_server.is_none() {
            self.initialize_components().await?;
        }

        // 启动数据处理器
        if let Some(mut data_processor) = self.data_processor.take() {
            let handle = tokio::spawn(async move { data_processor.start_processing().await });
            self.task_handles.push(handle);
            info!("✓ Data processor started");
        }

        // 启动撮合引擎
        if let Some(mut matching_engine) = self.matching_engine.take() {
            let handle = tokio::spawn(async move { matching_engine.start().await });
            self.task_handles.push(handle);
            info!("✓ Matching engine started");
        }

        // 启动WebSocket数据分发器
        if let Some(mut websocket_distributor) = self.websocket_distributor.take() {
            let handle =
                tokio::spawn(async move { websocket_distributor.start_distribution().await });
            self.task_handles.push(handle);
            info!("✓ WebSocket distributor started");
        }

        // 启动HTTP服务器
        if let Some(http_server) = self.http_server.take() {
            let handle = tokio::spawn(async move { http_server.start().await });
            self.task_handles.push(handle);
            info!("✓ HTTP server started");
        }

        // 启动WebSocket服务器
        if let Some(websocket_server) = self.websocket_server.take() {
            let handle = tokio::spawn(async move { websocket_server.start().await });
            self.task_handles.push(handle);
            info!("✓ WebSocket server started");
        }

        info!("All framework components started successfully");
        Ok(())
    }

    /// 等待所有组件完成
    pub async fn wait_for_completion(&mut self) -> Result<()> {
        info!("Waiting for all components to complete");

        while let Some(handle) = self.task_handles.pop() {
            match handle.await {
                Ok(result) => {
                    if let Err(e) = result {
                        error!("Component task failed: {}", e);
                    }
                }
                Err(e) => {
                    error!("Failed to join task: {}", e);
                }
            }
        }

        info!("All components completed");
        Ok(())
    }

    /// 优雅关闭框架
    pub async fn shutdown(&mut self) -> Result<()> {
        info!("Shutting down backtest framework");

        // 取消所有任务
        for handle in &self.task_handles {
            handle.abort();
        }

        // 等待任务完成或被取消
        while let Some(handle) = self.task_handles.pop() {
            match handle.await {
                Ok(_) => {}
                Err(e) if e.is_cancelled() => {
                    // 任务被取消是正常的
                }
                Err(e) => {
                    warn!("Task join error during shutdown: {}", e);
                }
            }
        }

        info!("Backtest framework shutdown completed");
        Ok(())
    }

    /// 获取框架状态信息
    pub fn get_status(&self) -> FrameworkStatus {
        FrameworkStatus {
            components_initialized: self.data_reader.is_some()
                || self.data_processor.is_some()
                || self.matching_engine.is_some()
                || self.http_server.is_some()
                || self.websocket_server.is_some(),
            active_tasks: self.task_handles.len(),
            message_bus_active: true, // 消息总线总是活跃的
        }
    }

    /// 获取数据流控制器的引用
    pub fn data_stream_controller(&self) -> Option<Arc<DataStreamController>> {
        self.data_stream_controller.clone()
    }
}

/// 框架状态信息
#[derive(Debug, Clone)]
pub struct FrameworkStatus {
    pub components_initialized: bool,
    pub active_tasks: usize,
    pub message_bus_active: bool,
}

impl Drop for BacktestFramework {
    fn drop(&mut self) {
        // 确保在框架被丢弃时取消所有任务
        for handle in &self.task_handles {
            handle.abort();
        }
    }
}
